<div id='PLAYER_ID' class='player' style="max-width: MAX_WIDTH;">
    <div class='spectrogram' style="padding-top: PADDING_AMOUNT;">
        <div class='overlay'></div>
        <div class='underlay'>
            <img>
        </div>
    </div>

    <div class='audio-controls'>
        <button id="playpause" disabled class='playpause' title="play">
            <svg class='play-img' width="14px" height="19px" viewBox="0 0 14 19">
                <polygon id="Triangle" fill="#000000" transform="translate(9, 9.5) rotate(90) translate(-7, -9.5) " points="7 2.5 16.5 16.5 -2.5 16.5"></polygon>
            </svg>
            <svg class='pause-img' width="16px" height="19px" viewBox="0 0 16 19">
                <g fill="#000000" stroke="#000000">
                    <rect id="Rectangle" x="0.5" y="0.5" width="4" height="18"></rect>
                    <rect id="Rectangle" x="11.5" y="0.5" width="4" height="18"></rect>
                </g>
            </svg>
        </button>

        <audio class='play'>
            <source id='src'>
        </audio>
        <div class='response'>
            <canvas class='response-canvas'></canvas>
        </div>

        <button id="download" class='download' title="download">
            <svg class='download-img' x="0px" y="0px" viewBox="0 0 29.978 29.978" style="enable-background:new 0 0 29.978 29.978;" xml:space="preserve">
            <g>
                <path d="M25.462,19.105v6.848H4.515v-6.848H0.489v8.861c0,1.111,0.9,2.012,2.016,2.012h24.967c1.115,0,2.016-0.9,2.016-2.012
                    v-8.861H25.462z"/>
                <path d="M14.62,18.426l-5.764-6.965c0,0-0.877-0.828,0.074-0.828s3.248,0,3.248,0s0-0.557,0-1.416c0-2.449,0-6.906,0-8.723
                    c0,0-0.129-0.494,0.615-0.494c0.75,0,4.035,0,4.572,0c0.536,0,0.524,0.416,0.524,0.416c0,1.762,0,6.373,0,8.742
                    c0,0.768,0,1.266,0,1.266s1.842,0,2.998,0c1.154,0,0.285,0.867,0.285,0.867s-4.904,6.51-5.588,7.193
                    C15.092,18.979,14.62,18.426,14.62,18.426z"/>
            </g>
            </svg>
        </button>
    </div>
</div>

<script>
    var PLAYER_ID = new Player('PLAYER_ID')
    PLAYER_ID.load(
        "AUDIO_SRC",
        "IMAGE_SRC",
        "LEVELS_SRC"
    )
    window.addEventListener("resize", function() {PLAYER_ID.redraw()})
</script>

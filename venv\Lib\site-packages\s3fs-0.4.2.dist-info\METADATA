Metadata-Version: 2.1
Name: s3fs
Version: 0.4.2
Summary: Convenient Filesystem interface over S3
Home-page: http://github.com/dask/s3fs/
Maintainer: <PERSON>
Maintainer-email: <EMAIL>
License: BSD
Keywords: s3,boto
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Requires-Python: >= 3.5
Requires-Dist: botocore (>=1.12.91)
Requires-Dist: fsspec (>=0.6.0)

s3fs
====

|Build Status| |Doc Status|

S3FS builds on botocore_ to provide a convenient Python filesystem interface for S3.

View the documentation_ for s3fs.

.. _documentation: http://s3fs.readthedocs.io/en/latest/
.. _botocore: https://botocore.readthedocs.io/en/latest/

.. |Build Status| image:: https://travis-ci.org/dask/s3fs.svg?branch=master
    :target: https://travis-ci.org/dask/s3fs
    :alt: Build Status
.. |Doc Status| image:: https://readthedocs.org/projects/s3fs/badge/?version=latest
    :target: https://s3fs.readthedocs.io/en/latest/?badge=latest
    :alt: Documentation Status



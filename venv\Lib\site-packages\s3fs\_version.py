
# This file was generated by 'versioneer.py' (0.18) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2020-03-31T11:23:02-0400",
 "dirty": false,
 "error": null,
 "full-revisionid": "a396dc4b6f56f754de2ac55043d85fb8f9006b6e",
 "version": "0.4.2"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
